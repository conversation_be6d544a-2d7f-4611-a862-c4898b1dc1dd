<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Minecraft Server Manager{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-sm);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 4px;
            padding: 8px 16px !important;
        }

        .nav-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color) !important;
            transform: translateY(-1px);
        }

        .container {
            position: relative;
            z-index: 1;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
            border-bottom: 1px solid rgba(99, 102, 241, 0.2);
            padding: 1.25rem;
        }

        .card-title {
            color: var(--dark-color);
            font-weight: 600;
            margin: 0;
        }

        .status-online {
            color: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-offline {
            color: var(--danger-color);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .player-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .player-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .console-output {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-radius: var(--border-radius);
            border: 1px solid rgba(148, 163, 184, 0.2);
            position: relative;
        }

        .console-output::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(90deg, #ef4444 0%, #f59e0b 33%, #10b981 66%);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .console-output::after {
            content: '● ● ●';
            position: absolute;
            top: 8px;
            left: 15px;
            color: #1e293b;
            font-size: 12px;
            font-weight: bold;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 4px solid var(--info-color);
            transition: all 0.3s ease;
            animation: slideInRight 0.5s ease;
        }

        .activity-item:hover {
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .activity-join {
            border-left-color: var(--success-color);
            background: rgba(16, 185, 129, 0.1);
        }

        .activity-leave {
            border-left-color: var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .badge {
            border-radius: 6px;
            font-weight: 500;
            padding: 6px 12px;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid rgba(148, 163, 184, 0.3);
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(20px);
        }

        .modal-header {
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        }

        .modal-footer {
            border-top: 1px solid rgba(148, 163, 184, 0.2);
        }

        /* Loading animations */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Fade in animation for page content */
        .fade-in {
            animation: fadeIn 0.6s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(148, 163, 184, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(99, 102, 241, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(99, 102, 241, 0.5);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInLeft" href="{{ url_for('index') }}">
                <i class="fas fa-cube me-2"></i>Minecraft Server Manager
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'index' else '' }}" href="{{ url_for('index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'players' else '' }}" href="{{ url_for('players') }}">
                            <i class="fas fa-users me-1"></i>Players
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'console' else '' }}" href="{{ url_for('console') }}">
                            <i class="fas fa-terminal me-1"></i>Console
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="http://localhost:25580" target="_blank">
                            <i class="fas fa-folder me-1"></i>Files
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999; margin-top: 80px;">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="toast show animate__animated animate__slideInRight" role="alert" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header">
                            <i class="fas fa-{{ 'check-circle text-success' if category == 'success' else 'exclamation-triangle text-warning' if category == 'warning' else 'times-circle text-danger' }} me-2"></i>
                            <strong class="me-auto">{{ 'Success' if category == 'success' else 'Warning' if category == 'warning' else 'Error' }}</strong>
                            <small class="text-muted">just now</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            {{ message }}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container mt-5 pt-4 fade-in">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Modern Toast System
        class ToastManager {
            static show(message, type = 'success', duration = 5000) {
                const toastContainer = document.querySelector('.toast-container');
                const toastId = 'toast-' + Date.now();

                const iconMap = {
                    success: 'check-circle text-success',
                    error: 'times-circle text-danger',
                    warning: 'exclamation-triangle text-warning',
                    info: 'info-circle text-info'
                };

                const titleMap = {
                    success: 'Success',
                    error: 'Error',
                    warning: 'Warning',
                    info: 'Information'
                };

                const toastHTML = `
                    <div id="${toastId}" class="toast animate__animated animate__slideInRight" role="alert" data-bs-autohide="true" data-bs-delay="${duration}">
                        <div class="toast-header">
                            <i class="fas fa-${iconMap[type]} me-2"></i>
                            <strong class="me-auto">${titleMap[type]}</strong>
                            <small class="text-muted">just now</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                `;

                toastContainer.insertAdjacentHTML('beforeend', toastHTML);
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement);
                toast.show();

                // Remove from DOM after hiding
                toastElement.addEventListener('hidden.bs.toast', () => {
                    toastElement.remove();
                });
            }
        }

        // Loading state management
        class LoadingManager {
            static show(element) {
                element.classList.add('loading');
                element.disabled = true;
            }

            static hide(element) {
                element.classList.remove('loading');
                element.disabled = false;
            }
        }

        // Enhanced auto-refresh functionality
        let refreshInterval;
        let isRefreshing = false;

        async function refreshData() {
            if (isRefreshing) return;
            isRefreshing = true;

            try {
                if (window.location.pathname === '/') {
                    const response = await fetch('/api/status');
                    const data = await response.json();

                    if (data.players) {
                        const onlineCountEl = document.getElementById('online-count');
                        const maxPlayersEl = document.getElementById('max-players');

                        if (onlineCountEl && maxPlayersEl) {
                            // Animate number changes
                            animateNumber(onlineCountEl, parseInt(onlineCountEl.textContent), data.players.online_count);
                            animateNumber(maxPlayersEl, parseInt(maxPlayersEl.textContent), data.players.max_players);
                        }

                        // Update last refresh indicator
                        updateLastRefresh();
                    }
                } else if (window.location.pathname === '/players') {
                    // Refresh player data
                    const response = await fetch('/api/players');
                    const data = await response.json();
                    // Update player count badge
                    const badge = document.querySelector('.badge');
                    if (badge && data.online_count !== undefined) {
                        badge.textContent = `${data.online_count}/${data.max_players}`;
                    }
                }
            } catch (error) {
                console.log('Refresh error:', error);
            } finally {
                isRefreshing = false;
            }
        }

        function animateNumber(element, from, to) {
            if (from === to) return;

            const duration = 500;
            const steps = 20;
            const stepValue = (to - from) / steps;
            let current = from;
            let step = 0;

            const timer = setInterval(() => {
                step++;
                current += stepValue;

                if (step >= steps) {
                    element.textContent = to;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.round(current);
                }
            }, duration / steps);
        }

        function updateLastRefresh() {
            const refreshElements = document.querySelectorAll('.last-refresh');
            const now = new Date().toLocaleTimeString();
            refreshElements.forEach(el => el.textContent = now);
        }

        // Initialize auto-refresh
        function startAutoRefresh(interval = 30000) {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(refreshData, interval);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // Enhanced form handling
        function handleFormSubmit(form, successMessage) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            LoadingManager.show(submitBtn);
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Store success message for after page reload
            if (successMessage) {
                sessionStorage.setItem('pendingToast', JSON.stringify({
                    message: successMessage,
                    type: 'success'
                }));
            }

            // Let the form submit normally
            return true;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check for pending toast messages
            const pendingToast = sessionStorage.getItem('pendingToast');
            if (pendingToast) {
                try {
                    const toastData = JSON.parse(pendingToast);
                    ToastManager.show(toastData.message, toastData.type);
                    sessionStorage.removeItem('pendingToast');
                } catch (e) {
                    console.error('Error parsing pending toast:', e);
                    sessionStorage.removeItem('pendingToast');
                }
            }

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize toasts
            const toastElList = [].slice.call(document.querySelectorAll('.toast'));
            toastElList.map(function (toastEl) {
                return new bootstrap.Toast(toastEl);
            });

            // Start auto-refresh
            startAutoRefresh();

            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading states to buttons
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        LoadingManager.show(submitBtn);
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    }
                });
            });
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            ToastManager.show('An unexpected error occurred. Please try again.', 'error');
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
