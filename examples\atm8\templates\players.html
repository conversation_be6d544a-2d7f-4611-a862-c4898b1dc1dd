{% extends "base.html" %}

{% block title %}Players - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Online Players
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary fs-6 animate__animated animate__pulse animate__infinite">
                        {{ online_players.online_count }}/{{ online_players.max_players }}
                    </span>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshPlayers()" data-bs-toggle="tooltip" title="Refresh players">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if detailed_players %}
                <div class="row g-3">
                    {% for player in detailed_players %}
                    <div class="col-md-6">
                        <div class="card player-card border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                            <i class="fas fa-user text-success"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1 fw-bold">{{ player.name }}</h6>
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>Online
                                        </span>
                                    </div>
                                </div>

                                <div class="row g-2 mb-3">
                                    {% if player.position %}
                                    <div class="col-12">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                            <small>
                                                <strong>Position:</strong>
                                                {{ "%.1f"|format(player.position.x) }},
                                                {{ "%.1f"|format(player.position.y) }},
                                                {{ "%.1f"|format(player.position.z) }}
                                            </small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.health %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-heart me-2 text-danger"></i>
                                            <small><strong>Health:</strong> {{ player.health }}/20</small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.gamemode is defined %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-gamepad me-2 text-info"></i>
                                            <small><strong>Mode:</strong> {{ {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}.get(player.gamemode, "Unknown") }}</small>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="d-flex gap-1 flex-wrap">
                                    <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Kick player">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Ban player">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Teleport player">
                                        <i class="fas fa-location-arrow"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Give operator status">
                                        <i class="fas fa-crown"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendPrivateMessage('{{ player.name }}')" data-bs-toggle="tooltip" title="Send private message">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="animate__animated animate__fadeIn">
                        <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">No Players Online</h5>
                        <p class="text-muted">Players will appear here when they join the server</p>
                        <button class="btn btn-outline-primary" onclick="refreshPlayers()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card animate__animated animate__fadeInRight">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Whitelist
                    <button class="btn btn-sm btn-outline-primary ms-auto" data-bs-toggle="modal" data-bs-target="#addWhitelistModal" data-bs-toggle="tooltip" title="Add player to whitelist">
                        <i class="fas fa-plus"></i>
                    </button>
                </h5>
            </div>
            <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                {% if whitelist %}
                    {% for player in whitelist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded animate__animated animate__fadeIn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-check text-success me-2"></i>
                            <span class="fw-medium">{{ player }}</span>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="confirmAction('Remove {{ player }} from whitelist?', 'removeFromWhitelist', '{{ player }}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-list fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No whitelisted players</p>
                    <small class="text-muted">Whitelist is currently disabled</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4 animate__animated animate__fadeInRight animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ban me-2"></i>Banned Players
                </h5>
            </div>
            <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                {% if banlist %}
                    {% for player in banlist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-danger bg-opacity-10 rounded animate__animated animate__fadeIn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-slash text-danger me-2"></i>
                            <span class="fw-medium">{{ player }}</span>
                        </div>
                        <form method="POST" action="{{ url_for('unban_player') }}" style="display: inline;" data-success-message="Player unbanned successfully!">
                            <input type="hidden" name="player_name" value="{{ player }}">
                            <button type="submit" class="btn btn-sm btn-outline-success" data-bs-toggle="tooltip" title="Unban player">
                                <i class="fas fa-undo"></i>
                            </button>
                        </form>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-shield-alt fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No banned players</p>
                    <small class="text-muted">Server is clean!</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4 animate__animated animate__fadeInRight animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button class="btn btn-outline-warning d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#kickModal">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Kick Player</div>
                            <small class="text-muted">Remove player temporarily</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-danger d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#banModal">
                        <i class="fas fa-ban me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Ban Player</div>
                            <small class="text-muted">Permanently ban player</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-success d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#opModal">
                        <i class="fas fa-crown me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Give Operator</div>
                            <small class="text-muted">Grant admin privileges</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-info d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#teleportModal">
                        <i class="fas fa-location-arrow me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Teleport Player</div>
                            <small class="text-muted">Move player to location</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kick Modal -->
<div class="modal fade" id="kickModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-warning bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-sign-out-alt text-warning me-2"></i>
                    Kick Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('kick_player') }}" data-success-message="Player kicked successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will temporarily remove the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="kickPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="kickPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="kickReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="kickReason" name="reason" value="Kicked by admin" placeholder="Enter kick reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-sign-out-alt me-2"></i>Kick Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-ban text-danger me-2"></i>
                    Ban Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ban_player') }}" data-success-message="Player banned successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-danger border-0 bg-danger bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will permanently ban the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="banPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="banPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="banReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="banReason" name="reason" value="Banned by admin" placeholder="Enter ban reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-ban me-2"></i>Ban Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Teleport Modal -->
<div class="modal fade" id="teleportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teleport Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teleport_player') }}" data-success-message="Player teleported successfully!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tpPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="tpPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="tpTarget" class="form-label">Target (player name or coordinates)</label>
                        <input type="text" class="form-control" id="tpTarget" name="target" placeholder="PlayerName or 0 64 0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Teleport</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OP Modal -->
<div class="modal fade" id="opModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Give Operator Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('op_player') }}" data-success-message="Player given operator status!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="opPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="opPlayerName" name="player_name" required>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Operator status gives players full administrative privileges.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Give OP</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

<!-- Add Whitelist Modal -->
<div class="modal fade" id="addWhitelistModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-user-plus text-success me-2"></i>
                    Add to Whitelist
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_whitelist') }}" data-success-message="Player added to whitelist!">
                <div class="modal-body p-4">
                    <div class="mb-3">
                        <label for="whitelistPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="whitelistPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>Add to Whitelist
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function setPlayerName(playerName) {
    document.getElementById('kickPlayerName').value = playerName;
    document.getElementById('banPlayerName').value = playerName;
    document.getElementById('tpPlayerName').value = playerName;
    document.getElementById('opPlayerName').value = playerName;
}

function refreshPlayers() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    fetch('/api/players')
        .then(response => response.json())
        .then(data => {
            // Update player count badge
            const badge = document.querySelector('.badge');
            if (badge && data.online_count !== undefined) {
                badge.textContent = `${data.online_count}/${data.max_players}`;
            }
            ToastManager.show('Player data refreshed!', 'success');
        })
        .catch(error => {
            ToastManager.show('Failed to refresh player data', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function sendPrivateMessage(playerName) {
    Swal.fire({
        title: `Send Message to ${playerName}`,
        input: 'text',
        inputPlaceholder: 'Enter your message...',
        showCancelButton: true,
        confirmButtonText: 'Send',
        confirmButtonColor: '#6366f1',
        cancelButtonColor: '#6b7280',
        inputValidator: (value) => {
            if (!value) {
                return 'Please enter a message!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Here you would send the message via API
            ToastManager.show(`Message sent to ${playerName}!`, 'success');
        }
    });
}

function confirmAction(message, action, playerName) {
    Swal.fire({
        title: 'Are you sure?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, proceed!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Execute the action
            if (action === 'removeFromWhitelist') {
                // Here you would call the API to remove from whitelist
                ToastManager.show(`${playerName} removed from whitelist!`, 'success');
            }
        }
    });
}

// Auto-refresh player data every 30 seconds
setInterval(() => {
    if (window.location.pathname === '/players') {
        fetch('/api/players')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('.badge');
                if (badge && data.online_count !== undefined) {
                    const currentCount = parseInt(badge.textContent.split('/')[0]);
                    if (currentCount !== data.online_count) {
                        badge.textContent = `${data.online_count}/${data.max_players}`;
                        badge.classList.add('animate__animated', 'animate__pulse');
                        setTimeout(() => {
                            badge.classList.remove('animate__animated', 'animate__pulse');
                        }, 1000);
                    }
                }
            })
            .catch(error => console.log('Auto-refresh error:', error));
    }
}, 30000);
</script>
{% endblock %}
